{"name": "animal-heaven", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:ci": "jest --ci --coverage --watchAll=false", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "docker:build": "docker build -t animal-heaven .", "docker:run": "docker run -p 3000:3000 animal-heaven", "docker:dev": "docker-compose -f docker-compose.yml --profile development up", "docker:prod": "docker-compose up", "analyze": "cross-env ANALYZE=true next build", "lighthouse": "lhci autorun", "server": "node server/index.js", "dev:server": "nodemon server/index.js"}, "dependencies": {"bcryptjs": "^3.0.2", "clsx": "^2.1.1", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^5.1.0", "express-rate-limit": "^8.0.0", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.525.0", "mongoose": "^8.16.3", "multer": "^2.0.1", "next": "15.4.1", "nodemailer": "^7.0.5", "react": "19.1.0", "react-dom": "19.1.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/jest": "^29.5.0", "@testing-library/react": "^14.0.0", "@testing-library/jest-dom": "^6.0.0", "@testing-library/user-event": "^14.0.0", "@playwright/test": "^1.40.0", "jest": "^29.5.0", "jest-environment-jsdom": "^29.5.0", "cross-env": "^7.0.3", "@lhci/cli": "^0.12.0", "eslint": "^9", "eslint-config-next": "15.4.1", "tailwindcss": "^4", "typescript": "^5"}}
import type { <PERSON>ada<PERSON> } from "next";
import { Inter, Poppins } from "next/font/google";
import "./globals.css";
import { AuthProvider } from "@/contexts/AuthContext";
import { ThemeProvider } from "@/contexts/ThemeContext";
import { LanguageProvider } from "@/contexts/LanguageContext";
import { NotificationProvider } from "@/contexts/NotificationContext";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
  display: "swap",
});

const poppins = Poppins({
  subsets: ["latin"],
  variable: "--font-poppins",
  weight: ["300", "400", "500", "600", "700", "800"],
  display: "swap",
});

export const metadata: Metadata = {
  title: "Animal Heaven - Find Your Perfect Companion",
  description: "Connect with loving pets in need of forever homes. Browse adoptable dogs, cats, and other animals from trusted shelters. Every adoption saves a life.",
  keywords: ["pet adoption", "animal shelter", "dogs", "cats", "rescue animals", "pet care", "animal welfare"],
  authors: [{ name: "Animal Heaven Team" }],
  creator: "Animal Heaven",
  publisher: "Animal Heaven",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL("https://animalheaven.org"),
  openGraph: {
    title: "Animal Heaven - Find Your Perfect Companion",
    description: "Connect with loving pets in need of forever homes. Browse adoptable dogs, cats, and other animals from trusted shelters.",
    url: "https://animalheaven.org",
    siteName: "Animal Heaven",
    images: [
      {
        url: "/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "Animal Heaven - Pet Adoption Platform",
      },
    ],
    locale: "en_US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Animal Heaven - Find Your Perfect Companion",
    description: "Connect with loving pets in need of forever homes. Browse adoptable dogs, cats, and other animals from trusted shelters.",
    images: ["/og-image.jpg"],
    creator: "@animalheaven",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  verification: {
    google: "your-google-verification-code",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="scroll-smooth">
      <head>
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
        <link rel="manifest" href="/site.webmanifest" />
        <meta name="theme-color" content="#f97316" />
      </head>
      <body
        className={`${inter.variable} ${poppins.variable} font-secondary antialiased`}
      >
        <ThemeProvider>
          <LanguageProvider>
            <NotificationProvider>
              <AuthProvider>
                {children}
              </AuthProvider>
            </NotificationProvider>
          </LanguageProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}

{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/STUDY/Projects/Animal%20heaven/animal-heaven/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useState, useEffect } from 'react';\n\ninterface User {\n  id: string;\n  email: string;\n  firstName: string;\n  lastName: string;\n  phone?: string;\n  address?: string;\n  role: 'user' | 'volunteer' | 'staff' | 'admin';\n  profileImage?: string;\n  preferences: {\n    petTypes: string[];\n    notifications: boolean;\n    newsletter: boolean;\n  };\n  createdAt: string;\n  lastLogin: string;\n}\n\ninterface AuthContextType {\n  user: User | null;\n  isLoading: boolean;\n  isAuthenticated: boolean;\n  login: (email: string, password: string) => Promise<void>;\n  register: (userData: RegisterData) => Promise<void>;\n  logout: () => void;\n  updateProfile: (userData: Partial<User>) => Promise<void>;\n  resetPassword: (email: string) => Promise<void>;\n}\n\ninterface RegisterData {\n  email: string;\n  password: string;\n  firstName: string;\n  lastName: string;\n  phone?: string;\n  address?: string;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n  const [user, setUser] = useState<User | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n\n  // Simulate API calls - in real app, these would be actual API calls\n  const login = async (email: string, password: string): Promise<void> => {\n    setIsLoading(true);\n    try {\n      // Simulate API call\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      \n      // Mock user data\n      const mockUser: User = {\n        id: '1',\n        email,\n        firstName: 'John',\n        lastName: 'Doe',\n        phone: '+91 98765 43210',\n        address: '123 Pet Street, Animal City',\n        role: 'user',\n        profileImage: '/images/users/john-doe.jpg',\n        preferences: {\n          petTypes: ['dog', 'cat'],\n          notifications: true,\n          newsletter: true\n        },\n        createdAt: '2024-01-01T00:00:00Z',\n        lastLogin: new Date().toISOString()\n      };\n      \n      setUser(mockUser);\n      localStorage.setItem('auth_token', 'mock_token_123');\n      localStorage.setItem('user', JSON.stringify(mockUser));\n    } catch (error) {\n      throw new Error('Invalid credentials');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const register = async (userData: RegisterData): Promise<void> => {\n    setIsLoading(true);\n    try {\n      // Simulate API call\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      \n      const newUser: User = {\n        id: Date.now().toString(),\n        email: userData.email,\n        firstName: userData.firstName,\n        lastName: userData.lastName,\n        phone: userData.phone,\n        address: userData.address,\n        role: 'user',\n        preferences: {\n          petTypes: [],\n          notifications: true,\n          newsletter: false\n        },\n        createdAt: new Date().toISOString(),\n        lastLogin: new Date().toISOString()\n      };\n      \n      setUser(newUser);\n      localStorage.setItem('auth_token', 'mock_token_123');\n      localStorage.setItem('user', JSON.stringify(newUser));\n    } catch (error) {\n      throw new Error('Registration failed');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const logout = () => {\n    setUser(null);\n    localStorage.removeItem('auth_token');\n    localStorage.removeItem('user');\n  };\n\n  const updateProfile = async (userData: Partial<User>): Promise<void> => {\n    if (!user) throw new Error('No user logged in');\n    \n    setIsLoading(true);\n    try {\n      // Simulate API call\n      await new Promise(resolve => setTimeout(resolve, 500));\n      \n      const updatedUser = { ...user, ...userData };\n      setUser(updatedUser);\n      localStorage.setItem('user', JSON.stringify(updatedUser));\n    } catch (error) {\n      throw new Error('Profile update failed');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const resetPassword = async (email: string): Promise<void> => {\n    // Simulate API call\n    await new Promise(resolve => setTimeout(resolve, 1000));\n    // In real app, this would send a password reset email\n  };\n\n  // Check for existing session on mount\n  useEffect(() => {\n    const token = localStorage.getItem('auth_token');\n    const savedUser = localStorage.getItem('user');\n    \n    if (token && savedUser) {\n      try {\n        const parsedUser = JSON.parse(savedUser);\n        setUser(parsedUser);\n      } catch (error) {\n        localStorage.removeItem('auth_token');\n        localStorage.removeItem('user');\n      }\n    }\n    \n    setIsLoading(false);\n  }, []);\n\n  const value: AuthContextType = {\n    user,\n    isLoading,\n    isAuthenticated: !!user,\n    login,\n    register,\n    logout,\n    updateProfile,\n    resetPassword\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n\nexport default AuthContext;\n"], "names": [], "mappings": ";;;;;;AAEA;AAFA;;;AA0CA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,MAAM,UAAU;IACrB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAEO,MAAM,eAAwD,CAAC,EAAE,QAAQ,EAAE;IAChF,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,oEAAoE;IACpE,MAAM,QAAQ,OAAO,OAAe;QAClC,aAAa;QACb,IAAI;YACF,oBAAoB;YACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,iBAAiB;YACjB,MAAM,WAAiB;gBACrB,IAAI;gBACJ;gBACA,WAAW;gBACX,UAAU;gBACV,OAAO;gBACP,SAAS;gBACT,MAAM;gBACN,cAAc;gBACd,aAAa;oBACX,UAAU;wBAAC;wBAAO;qBAAM;oBACxB,eAAe;oBACf,YAAY;gBACd;gBACA,WAAW;gBACX,WAAW,IAAI,OAAO,WAAW;YACnC;YAEA,QAAQ;YACR,aAAa,OAAO,CAAC,cAAc;YACnC,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC;QAC9C,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,MAAM;QAClB,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,WAAW,OAAO;QACtB,aAAa;QACb,IAAI;YACF,oBAAoB;YACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,MAAM,UAAgB;gBACpB,IAAI,KAAK,GAAG,GAAG,QAAQ;gBACvB,OAAO,SAAS,KAAK;gBACrB,WAAW,SAAS,SAAS;gBAC7B,UAAU,SAAS,QAAQ;gBAC3B,OAAO,SAAS,KAAK;gBACrB,SAAS,SAAS,OAAO;gBACzB,MAAM;gBACN,aAAa;oBACX,UAAU,EAAE;oBACZ,eAAe;oBACf,YAAY;gBACd;gBACA,WAAW,IAAI,OAAO,WAAW;gBACjC,WAAW,IAAI,OAAO,WAAW;YACnC;YAEA,QAAQ;YACR,aAAa,OAAO,CAAC,cAAc;YACnC,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC;QAC9C,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,MAAM;QAClB,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,SAAS;QACb,QAAQ;QACR,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;IAC1B;IAEA,MAAM,gBAAgB,OAAO;QAC3B,IAAI,CAAC,MAAM,MAAM,IAAI,MAAM;QAE3B,aAAa;QACb,IAAI;YACF,oBAAoB;YACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,MAAM,cAAc;gBAAE,GAAG,IAAI;gBAAE,GAAG,QAAQ;YAAC;YAC3C,QAAQ;YACR,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC;QAC9C,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,MAAM;QAClB,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,gBAAgB,OAAO;QAC3B,oBAAoB;QACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;IACjD,sDAAsD;IACxD;IAEA,sCAAsC;IACtC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,aAAa,OAAO,CAAC;QACnC,MAAM,YAAY,aAAa,OAAO,CAAC;QAEvC,IAAI,SAAS,WAAW;YACtB,IAAI;gBACF,MAAM,aAAa,KAAK,KAAK,CAAC;gBAC9B,QAAQ;YACV,EAAE,OAAO,OAAO;gBACd,aAAa,UAAU,CAAC;gBACxB,aAAa,UAAU,CAAC;YAC1B;QACF;QAEA,aAAa;IACf,GAAG,EAAE;IAEL,MAAM,QAAyB;QAC7B;QACA;QACA,iBAAiB,CAAC,CAAC;QACnB;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 167, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/STUDY/Projects/Animal%20heaven/animal-heaven/src/contexts/ThemeContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useState, useEffect } from 'react';\n\ntype Theme = 'light' | 'dark' | 'system';\n\ninterface ThemeContextType {\n  theme: Theme;\n  actualTheme: 'light' | 'dark';\n  setTheme: (theme: Theme) => void;\n  toggleTheme: () => void;\n}\n\nconst ThemeContext = createContext<ThemeContextType | undefined>(undefined);\n\nexport const useTheme = () => {\n  const context = useContext(ThemeContext);\n  if (context === undefined) {\n    throw new Error('useTheme must be used within a ThemeProvider');\n  }\n  return context;\n};\n\nexport const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n  const [theme, setThemeState] = useState<Theme>('system');\n  const [actualTheme, setActualTheme] = useState<'light' | 'dark'>('light');\n\n  // Get system preference\n  const getSystemTheme = (): 'light' | 'dark' => {\n    if (typeof window !== 'undefined') {\n      return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';\n    }\n    return 'light';\n  };\n\n  // Update actual theme based on theme setting\n  const updateActualTheme = (newTheme: Theme) => {\n    let resolvedTheme: 'light' | 'dark';\n    \n    if (newTheme === 'system') {\n      resolvedTheme = getSystemTheme();\n    } else {\n      resolvedTheme = newTheme;\n    }\n    \n    setActualTheme(resolvedTheme);\n    \n    // Update document class and localStorage\n    if (typeof window !== 'undefined') {\n      const root = document.documentElement;\n      root.classList.remove('light', 'dark');\n      root.classList.add(resolvedTheme);\n      localStorage.setItem('theme', newTheme);\n    }\n  };\n\n  // Set theme\n  const setTheme = (newTheme: Theme) => {\n    setThemeState(newTheme);\n    updateActualTheme(newTheme);\n  };\n\n  // Toggle between light and dark (ignoring system)\n  const toggleTheme = () => {\n    const newTheme = actualTheme === 'light' ? 'dark' : 'light';\n    setTheme(newTheme);\n  };\n\n  // Initialize theme on mount\n  useEffect(() => {\n    // Get saved theme from localStorage\n    const savedTheme = localStorage.getItem('theme') as Theme;\n    const initialTheme = savedTheme || 'system';\n    \n    setThemeState(initialTheme);\n    updateActualTheme(initialTheme);\n\n    // Listen for system theme changes\n    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n    const handleSystemThemeChange = () => {\n      if (theme === 'system') {\n        updateActualTheme('system');\n      }\n    };\n\n    mediaQuery.addEventListener('change', handleSystemThemeChange);\n    return () => mediaQuery.removeEventListener('change', handleSystemThemeChange);\n  }, []);\n\n  // Update actual theme when theme changes\n  useEffect(() => {\n    updateActualTheme(theme);\n  }, [theme]);\n\n  const value: ThemeContextType = {\n    theme,\n    actualTheme,\n    setTheme,\n    toggleTheme\n  };\n\n  return (\n    <ThemeContext.Provider value={value}>\n      {children}\n    </ThemeContext.Provider>\n  );\n};\n\nexport default ThemeContext;\n"], "names": [], "mappings": ";;;;;;AAEA;AAFA;;;AAaA,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAgC;AAE1D,MAAM,WAAW;IACtB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAEO,MAAM,gBAAyD,CAAC,EAAE,QAAQ,EAAE;IACjF,MAAM,CAAC,OAAO,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;IAEjE,wBAAwB;IACxB,MAAM,iBAAiB;QACrB;;QAGA,OAAO;IACT;IAEA,6CAA6C;IAC7C,MAAM,oBAAoB,CAAC;QACzB,IAAI;QAEJ,IAAI,aAAa,UAAU;YACzB,gBAAgB;QAClB,OAAO;YACL,gBAAgB;QAClB;QAEA,eAAe;QAEf,yCAAyC;QACzC;;IAMF;IAEA,YAAY;IACZ,MAAM,WAAW,CAAC;QAChB,cAAc;QACd,kBAAkB;IACpB;IAEA,kDAAkD;IAClD,MAAM,cAAc;QAClB,MAAM,WAAW,gBAAgB,UAAU,SAAS;QACpD,SAAS;IACX;IAEA,4BAA4B;IAC5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,oCAAoC;QACpC,MAAM,aAAa,aAAa,OAAO,CAAC;QACxC,MAAM,eAAe,cAAc;QAEnC,cAAc;QACd,kBAAkB;QAElB,kCAAkC;QAClC,MAAM,aAAa,OAAO,UAAU,CAAC;QACrC,MAAM,0BAA0B;YAC9B,IAAI,UAAU,UAAU;gBACtB,kBAAkB;YACpB;QACF;QAEA,WAAW,gBAAgB,CAAC,UAAU;QACtC,OAAO,IAAM,WAAW,mBAAmB,CAAC,UAAU;IACxD,GAAG,EAAE;IAEL,yCAAyC;IACzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,kBAAkB;IACpB,GAAG;QAAC;KAAM;IAEV,MAAM,QAA0B;QAC9B;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,aAAa,QAAQ;QAAC,OAAO;kBAC3B;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 262, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/STUDY/Projects/Animal%20heaven/animal-heaven/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;KAElC;IACL,IAAIF,QAAQC,GAAG,CAACK,yBAAyB,EAAE;;SAcpC;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;QAGT,OAAO;;IAOT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 283, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/STUDY/Projects/Animal%20heaven/animal-heaven/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 290, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/STUDY/Projects/Animal%20heaven/animal-heaven/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,KAAK", "ignoreList": [0], "debugId": null}}]}
# 🐾 Animal Heaven - Pet Adoption Platform

A modern, warm, and emotionally engaging pet adoption website built with Next.js, TypeScript, and a comprehensive design system.

## ✨ Features

### 🎨 Design System
- **Warm & Pet-Friendly Aesthetics**: Earth tones, soft curves, and pet-themed elements
- **Comprehensive Component Library**: Reusable UI components with consistent styling
- **Responsive Design**: Mobile-first approach with seamless desktop experience
- **Accessibility**: WCAG compliant with proper focus management and screen reader support
- **Dark/Light Mode**: Automatic theme switching based on user preference

### 🏠 Core Pages
- **Hero Section**: Interactive carousel with search functionality
- **Pet Adoption**: Advanced filtering and pet profile pages
- **User Dashboard**: Profile management and application tracking
- **Volunteer Portal**: Volunteer registration and management
- **Donation System**: One-time and recurring donation options
- **Pet Care Resources**: Educational content and guides
- **Gallery**: Community photos and success stories
- **Admin Panel**: Shelter staff management interface

### 🔧 Technical Features
- **Full-Stack Architecture**: Next.js frontend with Node.js/Express backend
- **Database**: MongoDB with Mongoose ODM
- **Authentication**: JWT-based user authentication
- **File Upload**: Image and document handling
- **Email System**: Automated notifications and communications
- **Search & Filtering**: Advanced pet search capabilities
- **Real-time Updates**: Live status updates for applications

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- MongoDB (local or cloud)
- npm or yarn

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-username/animal-heaven.git
   cd animal-heaven
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Setup**
   Create a `.env.local` file in the root directory:
   ```env
   # Database
   MONGODB_URI=mongodb://localhost:27017/animal-heaven

   # Authentication
   JWT_SECRET=your-super-secret-jwt-key

   # Email (Optional)
   SMTP_HOST=smtp.gmail.com
   SMTP_PORT=587
   SMTP_USER=<EMAIL>
   SMTP_PASS=your-app-password
   ```

4. **Start the development server**
   ```bash
   npm run dev
   ```

5. **Start the backend server** (in a new terminal)
   ```bash
   npm run dev:server
   ```

6. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 🎨 Design System

### Color Palette
- **Primary**: Warm orange tones (#f97316)
- **Secondary**: Soft green accents (#22c55e)
- **Accent**: Earthy browns (#bfa094)
- **Neutral**: Warm grays (#78716c)

### Typography
- **Primary Font**: Poppins (headings, buttons, brand elements)
- **Secondary Font**: Inter (body text, UI elements)

### Components
- **Buttons**: 5 variants with multiple sizes
- **Cards**: 4 variants for different use cases
- **Forms**: Accessible inputs with validation
- **Badges**: Status and category indicators
- **Navigation**: Responsive with mobile menu
- **Pet Cards**: Specialized components for pet display

## 📁 Project Structure

```
animal-heaven/
├── src/
│   ├── app/                 # Next.js app directory
│   │   ├── globals.css      # Global styles and design tokens
│   │   ├── layout.tsx       # Root layout
│   │   └── page.tsx         # Home page
│   ├── components/          # React components
│   │   ├── ui/              # Basic UI components
│   │   ├── layout/          # Layout components
│   │   ├── pets/            # Pet-specific components
│   │   └── sections/        # Page sections
│   └── lib/
│       └── utils.ts         # Utility functions
├── server/                  # Backend API
│   ├── models/              # Database models
│   ├── routes/              # API routes
│   ├── middleware/          # Custom middleware
│   └── index.js             # Server entry point
├── public/                  # Static assets
├── DESIGN_SYSTEM.md         # Design system documentation
└── README.md               # This file
```

## 🛠️ Available Scripts

### Development
- `npm run dev` - Start Next.js development server with Turbopack
- `npm run dev:server` - Start backend development server
- `npm run lint` - Run ESLint
- `npm run lint:fix` - Run ESLint with auto-fix
- `npm run type-check` - Run TypeScript type checking

### Testing
- `npm run test` - Run unit tests with Jest
- `npm run test:watch` - Run tests in watch mode
- `npm run test:ci` - Run tests with coverage for CI
- `npm run test:e2e` - Run end-to-end tests with Playwright
- `npm run test:e2e:ui` - Run E2E tests with UI mode

### Production
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run analyze` - Analyze bundle size
- `npm run lighthouse` - Run Lighthouse performance audit

### Docker
- `npm run docker:build` - Build Docker image
- `npm run docker:run` - Run Docker container
- `npm run docker:dev` - Start development environment with Docker Compose
- `npm run docker:prod` - Start production environment with Docker Compose

## 🧪 Testing Strategy

### Unit Testing
- **Framework**: Jest with React Testing Library
- **Coverage**: 70%+ threshold for branches, functions, lines, statements
- **Components**: All UI components have comprehensive tests
- **Utilities**: All utility functions are tested

### Integration Testing
- **API Routes**: All endpoints tested with mock data
- **Context Providers**: Authentication and theme contexts tested
- **Form Validation**: All forms tested for validation logic

### End-to-End Testing
- **Framework**: Playwright
- **Coverage**: Critical user journeys (adoption flow, registration, etc.)
- **Cross-browser**: Chrome, Firefox, Safari
- **Mobile**: Responsive design testing

### Performance Testing
- **Lighthouse CI**: Automated performance audits
- **Core Web Vitals**: Monitoring of LCP, FID, CLS
- **Bundle Analysis**: Regular bundle size monitoring

## 🚀 Deployment Options

### Vercel (Recommended)
1. **Connect Repository**: Link your GitHub repository to Vercel
2. **Environment Variables**: Set up environment variables in Vercel dashboard
3. **Automatic Deployment**: Deploys automatically on push to main branch
4. **Preview Deployments**: Every PR gets a preview deployment

### Docker Deployment
```bash
# Build and run with Docker
npm run docker:build
npm run docker:run

# Or use Docker Compose for full stack
npm run docker:prod
```

### Traditional Hosting
```bash
# Build the application
npm run build

# Start the production server
npm run start
```

## 🔧 Configuration

### Environment Variables
Create a `.env.local` file with the following variables:

```env
# Database
DATABASE_URL=mongodb://localhost:27017/animal-heaven
MONGODB_URI=mongodb://localhost:27017/animal-heaven

# Authentication
NEXTAUTH_SECRET=your-super-secret-key-here
NEXTAUTH_URL=http://localhost:3000

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# File Upload
UPLOAD_DIR=./public/uploads
MAX_FILE_SIZE=5242880

# External APIs
GOOGLE_MAPS_API_KEY=your-google-maps-key
STRIPE_SECRET_KEY=your-stripe-secret-key
STRIPE_PUBLISHABLE_KEY=your-stripe-publishable-key

# Analytics
GOOGLE_ANALYTICS_ID=GA_MEASUREMENT_ID
```

### Performance Optimization
- **Image Optimization**: Next.js Image component with lazy loading
- **Code Splitting**: Automatic route-based code splitting
- **Bundle Analysis**: Regular monitoring with `npm run analyze`
- **Caching**: Static generation and ISR for optimal performance

## 📊 Monitoring & Analytics

### Performance Monitoring
- **Lighthouse CI**: Automated performance audits on every deployment
- **Core Web Vitals**: Real user monitoring
- **Bundle Size**: Tracking with size-limit

### Error Tracking
- **Sentry**: Error tracking and performance monitoring
- **Console Logging**: Structured logging for debugging

### User Analytics
- **Google Analytics**: User behavior tracking
- **Conversion Tracking**: Adoption success rates
- **A/B Testing**: Feature flag support

## 🤝 Contributing

We welcome contributions! Please follow these steps:

1. **Fork the Repository**
2. **Create a Feature Branch**: `git checkout -b feature/amazing-feature`
3. **Make Changes**: Follow our coding standards
4. **Write Tests**: Ensure your changes are tested
5. **Run Tests**: `npm run test` and `npm run test:e2e`
6. **Commit Changes**: Use conventional commit messages
7. **Push to Branch**: `git push origin feature/amazing-feature`
8. **Open Pull Request**: Describe your changes clearly

### Code Standards
- **TypeScript**: Strict mode enabled
- **ESLint**: Follow the configured rules
- **Prettier**: Code formatting
- **Conventional Commits**: Use semantic commit messages

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Next.js Team**: For the amazing framework
- **Tailwind CSS**: For the utility-first CSS framework
- **Lucide**: For the beautiful icon library
- **Vercel**: For the deployment platform
- **Animal Shelters**: For inspiration and real-world requirements

## 📞 Support

- **Documentation**: Check the [Wiki](https://github.com/yourusername/animal-heaven/wiki)
- **Issues**: Report bugs on [GitHub Issues](https://github.com/yourusername/animal-heaven/issues)
- **Discussions**: Join [GitHub Discussions](https://github.com/yourusername/animal-heaven/discussions)
- **Email**: Contact <NAME_EMAIL>

---

<div align="center">
  <p><strong>Made with ❤️ for animals in need</strong></p>
  <p>🐾 Every pet deserves a loving home 🏠</p>

  [![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https://github.com/yourusername/animal-heaven)
  [![Run on Repl.it](https://replit.com/badge/github/yourusername/animal-heaven)](https://replit.com/new/github/yourusername/animal-heaven)
</div>
